import {
  <PERSON>odBoard01,
  <PERSON>od<PERSON>oard02,
  <PERSON>od<PERSON>oard03,
  MoodBoard04,
  <PERSON>odBoard05,
  <PERSON>odBoard06,
  <PERSON>odBoard07,
  <PERSON>odBoard08,
  <PERSON>odBoard09,
  <PERSON>odBoard10,
  <PERSON>odBoard11,
  <PERSON>od<PERSON>oard12,
} from '@/constants/images';
import { MoodBoardImage, <PERSON><PERSON>ontainer, StyleMoodBoardContainer } from './styles';
import { TouchableWithoutFeedback } from 'react-native';

type StyleMoodBoardProps = {
  rowsToShow?: number;
  onPress?: () => void;
};

export const StyleMoodBoard = ({ rowsToShow = 4, onPress }: StyleMoodBoardProps) => {
  const rows = [
    {
      id: 1,
      images: [MoodBoard01, MoodBoard02, MoodBoard03],
    },
    {
      id: 2,
      images: [MoodBoard04, MoodBoard05],
    },
    {
      id: 3,
      images: [MoodBoard06, MoodBoard07, <PERSON>od<PERSON>oard08, <PERSON>od<PERSON>oard09],
    },
    {
      id: 4,
      images: [MoodBoard10, <PERSON>od<PERSON>oard11, <PERSON><PERSON><PERSON><PERSON>d12],
    },
  ];

  const rowsToRender = rows.slice(0, rowsToShow);

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <StyleMoodBoardContainer>
        {rowsToRender.map((row) => (
          <RowContainer key={row.id}>
            {row.images.map((image, index) => (
              <MoodBoardImage key={index} source={image} />
            ))}
          </RowContainer>
        ))}
      </StyleMoodBoardContainer>
    </TouchableWithoutFeedback>
  );
};
